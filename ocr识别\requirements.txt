# This file lists the required packages for the OCR application.
# Use 'pip install -r requirements.txt' to install them.

# --- Core Application Framework ---
pyside6==6.7.0

# --- Image Handling and Clipboard ---
Pillow==10.3.0
pyperclip==1.8.2

# --- OCR Engine and Dependencies ---
# paddleocr requires numpy.
numpy==1.26.4
paddleocr==2.7.3

# --- PaddlePaddle Deep Learning Framework ---
# Choose ONE of the following lines depending on your hardware.
# For CPU-only execution (default):
paddlepaddle==2.6.1

# For GPU (NVIDIA CUDA) execution, comment out the line above and uncomment
# the line below. Make sure you have a compatible NVIDIA driver and CUDA toolkit installed.
# For more details on GPU setup, visit the official PaddlePaddle website:
# https://www.paddlepaddle.org.cn/install/quick
#
# paddlepaddle-gpu==2.6.1.post118

# GUI相关
tkinterdnd2>=0.3.0

# 图像处理
Pillow>=8.0.0

# 剪贴板操作
pyperclip>=1.8.0

# 其他依赖
numpy>=1.21.0
opencv-python>=4.5.0

# 注意事项：
# 1. 如果您有NVIDIA GPU且安装了CUDA 12.x，建议使用GPU版本以获得更好的性能
# 2. 如果遇到CUDA兼容性问题，可以回退到CPU版本
# 3. Windows用户可能需要安装Microsoft Visual C++ Redistributable
