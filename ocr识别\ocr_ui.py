import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from tkinterdnd2 import DND_FILES, TkinterDnD
import threading
import os
import tempfile
import time
import logging
import urllib.request
from PIL import Image, ImageTk, ImageGrab
import pyperclip

# 设置环境变量以减少PaddlePaddle的警告信息
os.environ['GLOG_minloglevel'] = '2'  # 只显示ERROR级别的日志
# 移除CUDA_VISIBLE_DEVICES设置，允许使用GPU

from paddleocr import PaddleOCR


class OCRProcessor:
    """OCR处理器类"""

    def __init__(self):
        self.ocr = None
        self.is_initialized = False
        self.init_start_time = None
        self.device_type = 'auto'  # 'auto', 'gpu', 'cpu'

        # 配置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)

    def check_gpu_support(self):
        """检查GPU支持情况"""
        try:
            import paddle
            return paddle.device.is_compiled_with_cuda() and paddle.device.cuda.device_count() > 0
        except Exception as e:
            self.logger.warning(f"检查GPU支持时出错: {e}")
            return False

    def initialize_ocr(self, device_type='auto', callback=None):
        """异步初始化OCR引擎"""
        def init_worker():
            self.init_start_time = time.time()
            self.device_type = device_type
            self.logger.info("开始初始化OCR引擎...")

            # 根据设备类型选择初始化方式
            if device_type == 'auto':
                # 自动检测，优先GPU
                if self.check_gpu_support():
                    device = 'gpu'
                    mode_name = "GPU模式（自动检测）"
                else:
                    device = 'cpu'
                    mode_name = "CPU模式（自动检测）"
            elif device_type == 'gpu':
                device = 'gpu'
                mode_name = "GPU模式（用户指定）"
            else:
                device = 'cpu'
                mode_name = "CPU模式（用户指定）"

            try:
                self.logger.info(f"尝试{mode_name}初始化...")
                self.ocr = PaddleOCR(
                    lang='ch',
                    device=device
                )
                elapsed_time = time.time() - self.init_start_time
                self.logger.info(f"OCR初始化成功（{mode_name}），耗时: {elapsed_time:.2f}秒")
                print(f"OCR初始化成功（{mode_name}），耗时: {elapsed_time:.2f}秒")
                self.is_initialized = True
                if callback:
                    callback(True, f"OCR初始化成功（{device.upper()}），耗时: {elapsed_time:.1f}秒")

            except Exception as e:
                self.logger.error(f"{mode_name}初始化失败: {e}")

                # 如果是GPU模式失败，且不是用户强制指定GPU，则尝试CPU模式
                if device == 'gpu' and device_type != 'gpu':
                    try:
                        self.logger.info("GPU初始化失败，尝试CPU模式...")
                        self.ocr = PaddleOCR(
                            lang='ch',
                            device='cpu'
                        )
                        elapsed_time = time.time() - self.init_start_time
                        self.logger.info(f"OCR初始化成功（CPU模式），耗时: {elapsed_time:.2f}秒")
                        print(f"OCR初始化成功（CPU模式），耗时: {elapsed_time:.2f}秒")
                        self.is_initialized = True
                        if callback:
                            callback(True, f"OCR初始化成功（CPU），耗时: {elapsed_time:.1f}秒")
                    except Exception as e2:
                        elapsed_time = time.time() - self.init_start_time
                        error_msg = f"OCR初始化失败，耗时: {elapsed_time:.1f}秒。错误: {str(e2)}"
                        self.logger.error(error_msg)
                        print(error_msg)
                        self.is_initialized = False
                        if callback:
                            callback(False, error_msg)
                else:
                    # 直接失败，不尝试其他模式
                    elapsed_time = time.time() - self.init_start_time
                    error_msg = f"OCR初始化失败，耗时: {elapsed_time:.1f}秒。错误: {str(e)}"
                    self.logger.error(error_msg)
                    print(error_msg)
                    self.is_initialized = False
                    if callback:
                        callback(False, error_msg)

        # 在后台线程中初始化
        thread = threading.Thread(target=init_worker, daemon=True)
        thread.start()
    
    def process_image(self, image_path):
        """处理图片并返回识别结果"""
        if not self.is_initialized or self.ocr is None:
            raise Exception("OCR引擎未初始化，请稍候...")

        start_time = time.time()
        self.logger.info(f"开始处理图片: {image_path}")

        try:
            # 检查图片文件是否存在和有效
            if not os.path.exists(image_path):
                raise Exception(f"图片文件不存在: {image_path}")

            # 检查文件大小
            file_size = os.path.getsize(image_path)
            if file_size == 0:
                raise Exception("图片文件为空")
            elif file_size > 50 * 1024 * 1024:  # 50MB
                raise Exception("图片文件过大（超过50MB）")

            self.logger.info(f"图片文件大小: {file_size / 1024:.1f} KB")

            # 使用兼容的OCR方法
            self.logger.info("开始OCR识别...")
            try:
                # 尝试新版本的predict方法
                result = self.ocr.predict(image_path)
                self.logger.info("使用predict方法识别")
            except (AttributeError, TypeError) as e:
                # 如果predict方法不存在或参数不兼容，使用旧版本的ocr方法
                self.logger.info(f"predict方法失败({e})，尝试ocr方法")
                try:
                    result = self.ocr.ocr(image_path, cls=True)
                    self.logger.info("使用ocr方法识别（带cls参数）")
                except TypeError:
                    # 如果cls参数不支持，使用最简单的调用
                    result = self.ocr.ocr(image_path)
                    self.logger.info("使用ocr方法识别（无cls参数）")
            except Exception as cuda_error:
                # 检查是否是CUDA相关错误
                error_str = str(cuda_error).lower()
                if any(keyword in error_str for keyword in ['cublas', 'cuda', 'gpu', 'preconditionnotmet']):
                    self.logger.warning(f"GPU处理失败，尝试CPU模式: {cuda_error}")
                    # 尝试重新初始化为CPU模式
                    try:
                        from paddleocr import PaddleOCR
                        self.ocr = PaddleOCR(lang='ch', device='cpu')
                        self.device_type = 'cpu'
                        self.logger.info("已自动切换到CPU模式")

                        # 重新尝试识别
                        result = self.ocr.ocr(image_path)
                        self.logger.info("CPU模式识别成功")
                    except Exception as cpu_error:
                        raise Exception(f"GPU和CPU模式都失败: GPU错误: {cuda_error}, CPU错误: {cpu_error}")
                else:
                    raise cuda_error

            processing_time = time.time() - start_time
            self.logger.info(f"OCR识别完成，耗时: {processing_time:.2f}秒")

            if result and result[0]:
                # 提取文本内容
                texts = []
                confidence_scores = []

                for line in result[0]:
                    if len(line) >= 2 and len(line[1]) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]
                        texts.append(text)
                        confidence_scores.append(confidence)

                if texts:
                    avg_confidence = sum(confidence_scores) / len(confidence_scores)
                    result_text = '\n'.join(texts)
                    self.logger.info(f"识别到 {len(texts)} 行文字，平均置信度: {avg_confidence:.2f}")
                    return result_text
                else:
                    return "未识别到有效文字内容"
            else:
                self.logger.warning("OCR返回空结果")
                return "未识别到文字内容"

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"图片处理失败（耗时{processing_time:.1f}秒）: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)


class OCRApp:
    """OCR UI应用程序"""

    def __init__(self):
        self.root = TkinterDnD.Tk()
        self.root.title("OCR文字识别工具 - 支持拖拽、粘贴、点击选择")
        self.root.minsize(1000, 600)

        # 初始化OCR处理器（不立即初始化）
        self.ocr_processor = OCRProcessor()

        # 当前图片路径和处理状态
        self.current_image_path = None
        self.is_processing = False
        self.ocr_ready = False
        self.init_start_time = None
        self.status_update_timer = None

        # 图片缩放相关
        self.current_image = None  # 原始PIL图片
        self.displayed_photo = None  # 显示的PhotoImage对象
        self.zoom_factor = 1.0
        self.image_offset_x = 0
        self.image_offset_y = 0

        # 创建UI
        self.create_widgets()
        self.setup_drag_drop()
        self.setup_keyboard_shortcuts()

        # 异步初始化OCR
        self.start_ocr_initialization()

        # 立即尝试居中
        self.center_window()

        # 再次延迟居中，确保生效
        self.root.after(500, self.center_window)

    def center_window(self):
        """将窗口居中显示"""
        try:
            # 设置窗口大小
            window_width = 1200
            window_height = 700

            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # 计算居中位置
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            # 确保窗口不会超出屏幕边界
            if x < 0:
                x = 0
            if y < 0:
                y = 0

            # 使用wm_geometry方法设置位置
            self.root.wm_geometry(f"{window_width}x{window_height}+{x}+{y}")

            # 强制更新
            self.root.update_idletasks()

        except Exception as e:
            # 静默处理错误，不影响程序运行
            pass

    def start_ocr_initialization(self):
        """开始OCR初始化"""
        self.status_var.set("正在初始化OCR引擎，首次运行需要下载模型文件（约300MB）...")
        self.progress_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.status_bar)
        self.progress_bar.start(10)

        # 更新拖拽区域提示
        self.drop_area.configure(
            text="🔄 正在初始化OCR引擎...\n\n📥 首次运行需要下载模型文件\n📦 大小约300MB，请耐心等待\n⏱️ 预计需要3-10分钟（取决于网速）\n\n💡 下载完成后使用会很快！",
            bg="#FFF3E0",
            fg="#F57C00",
            font=("Arial", 11)
        )

        # 异步初始化OCR
        device_type = getattr(self, 'device_var', None)
        device = device_type.get() if device_type else 'auto'
        self.ocr_processor.initialize_ocr(device, self.on_ocr_initialized)

        # 启动状态更新定时器
        self.init_start_time = time.time()
        self.start_status_update_timer()

    def start_status_update_timer(self):
        """启动状态更新定时器"""
        self.update_initialization_status()

    def update_initialization_status(self):
        """更新初始化状态显示"""
        if not self.ocr_ready and self.init_start_time:
            elapsed = time.time() - self.init_start_time
            minutes = int(elapsed // 60)
            seconds = int(elapsed % 60)

            if elapsed < 30:
                status_msg = f"正在初始化OCR引擎... ({seconds}秒) - 正在检查本地模型"
                tip_msg = f"🔄 正在初始化OCR引擎...\n\n🔍 检查本地模型文件\n⏱️ 已用时: {seconds}秒"
            elif elapsed < 60:
                status_msg = f"正在下载模型文件... ({seconds}秒) - 请保持网络连接"
                tip_msg = f"🔄 正在下载模型文件...\n\n📥 下载中，请保持网络连接\n📦 模型大小约300MB\n⏱️ 已用时: {seconds}秒\n\n💡 首次下载后使用会很快！"
            else:
                # 检查网络连接
                network_status = "🌐 网络连接正常" if self.check_network_connection() else "❌ 网络连接异常，请检查网络"
                status_msg = f"正在下载模型文件... ({minutes}分{seconds}秒) - 请耐心等待"
                tip_msg = f"🔄 正在下载模型文件...\n\n📥 下载中，请耐心等待\n📦 模型大小约300MB\n⏱️ 已用时: {minutes}分{seconds}秒\n{network_status}\n\n💡 下载完成后使用会很快！"

            self.status_var.set(status_msg)
            self.drop_area.configure(text=tip_msg)

            # 每2秒更新一次
            self.status_update_timer = self.root.after(2000, self.update_initialization_status)

    def stop_status_update_timer(self):
        """停止状态更新定时器"""
        if self.status_update_timer:
            self.root.after_cancel(self.status_update_timer)
            self.status_update_timer = None

    def check_network_connection(self):
        """检查网络连接"""
        try:
            urllib.request.urlopen('http://www.baidu.com', timeout=3)
            return True
        except:
            return False

    def on_ocr_initialized(self, success, message):
        """OCR初始化完成回调"""
        def update_ui():
            # 停止状态更新定时器
            self.stop_status_update_timer()

            self.progress_bar.stop()
            self.progress_bar.pack_forget()

            if success:
                self.ocr_ready = True
                self.status_var.set(f"OCR引擎初始化完成 - {message}")
                self.drop_area.configure(
                    text="📷 拖拽图片到此处\n\n📋 或按Ctrl+V粘贴",
                    bg="#E8F5E8",
                    fg="#2E7D32"
                )
                # 计算总耗时
                if self.init_start_time:
                    total_time = time.time() - self.init_start_time
                    self.status_var.set(f"OCR引擎初始化完成，耗时: {total_time:.1f}秒")

            else:
                self.status_var.set(f"OCR初始化失败: {message}")
                self.drop_area.configure(
                    text=f"❌ OCR初始化失败\n{message}\n\n点击重试或清理缓存",
                    bg="#FFEBEE",
                    fg="#C62828"
                )
                # 显示错误对话框，提供解决方案
                result = messagebox.askyesno(
                    "初始化失败",
                    f"OCR初始化失败：\n{message}\n\n可能的解决方案：\n"
                    "1. 检查网络连接\n"
                    "2. 清理模型缓存\n"
                    "3. 重启程序\n\n"
                    "是否要清理模型缓存并重试？"
                )
                if result:
                    self.clear_model_cache_and_retry()

        # 在主线程中更新UI
        self.root.after(0, update_ui)

    def clear_model_cache_and_retry(self):
        """清理模型缓存并重试初始化"""
        try:
            import shutil
            # PaddleOCR模型缓存路径
            cache_paths = [
                os.path.expanduser("~/.paddleocr"),
                os.path.expanduser("~/.paddlex"),
                os.path.expanduser("~/AppData/Local/.paddleocr"),
                os.path.expanduser("~/AppData/Local/.paddlex")
            ]

            cleared_any = False
            for cache_path in cache_paths:
                if os.path.exists(cache_path):
                    try:
                        shutil.rmtree(cache_path)
                        print(f"已清理缓存目录: {cache_path}")
                        cleared_any = True
                    except Exception as e:
                        print(f"清理缓存目录失败 {cache_path}: {e}")

            if cleared_any:
                messagebox.showinfo("缓存清理", "模型缓存已清理，正在重新初始化...")
                # 重新初始化
                self.ocr_processor = OCRProcessor()
                self.ocr_ready = False
                self.start_ocr_initialization()
            else:
                messagebox.showwarning("缓存清理", "未找到需要清理的缓存目录")

        except Exception as e:
            messagebox.showerror("错误", f"清理缓存时出错: {e}")

    def on_device_change(self):
        """设备选择变化时的处理"""
        device = self.device_var.get()

        # 检查GPU可用性
        if device == 'gpu' and not self.ocr_processor.check_gpu_support():
            messagebox.showwarning(
                "GPU不可用",
                "检测到您选择了GPU模式，但系统中没有可用的GPU或CUDA支持。\n\n"
                "可能的原因：\n"
                "1. 没有安装NVIDIA GPU\n"
                "2. 没有安装CUDA运行时库\n"
                "3. PaddlePaddle版本与CUDA版本不兼容\n\n"
                "建议选择'自动检测'或'CPU模式'。"
            )
            # 自动切换回自动检测模式
            self.device_var.set('auto')
            return

        if hasattr(self, 'ocr_ready') and self.ocr_ready:
            result = messagebox.askyesno(
                "设备变更",
                f"检测到设备设置变更为'{self.get_device_display_name(device)}'，\n"
                "是否重新初始化OCR引擎以应用新设置？"
            )
            if result:
                self.reinitialize_ocr_with_device()

    def get_device_display_name(self, device):
        """获取设备显示名称"""
        names = {
            'auto': '自动检测',
            'gpu': 'GPU加速',
            'cpu': 'CPU模式'
        }
        return names.get(device, device)

    def reinitialize_ocr_with_device(self):
        """使用新设备设置重新初始化OCR"""
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，无法重新初始化")
            return

        self.ocr_processor = OCRProcessor()
        self.ocr_ready = False
        device = self.device_var.get()

        # 更新状态显示
        device_names = {"auto": "自动检测", "gpu": "GPU加速", "cpu": "CPU模式"}
        self.status_var.set(f"正在使用{device_names.get(device, device)}模式重新初始化OCR引擎...")

        # 开始初始化
        self.start_ocr_initialization()

    def zoom_in(self):
        """放大图片"""
        if self.current_image:
            self.zoom_factor *= 1.2
            self.update_image_display()

    def zoom_out(self):
        """缩小图片"""
        if self.current_image:
            self.zoom_factor /= 1.2
            if self.zoom_factor < 0.1:
                self.zoom_factor = 0.1
            self.update_image_display()

    def fit_to_window(self):
        """适配窗口大小"""
        if self.current_image:
            self.zoom_factor = 1.0
            self.image_offset_x = 0
            self.image_offset_y = 0
            self.update_image_display()

    def on_mouse_wheel(self, event):
        """鼠标滚轮缩放"""
        if self.current_image:
            # 计算缩放
            if event.delta > 0:
                scale = 1.1
            else:
                scale = 0.9

            self.zoom_factor *= scale

            if self.zoom_factor < 0.1:
                self.zoom_factor = 0.1
            elif self.zoom_factor > 10.0:
                self.zoom_factor = 10.0

            # 更新显示
            self.update_image_display()

    def update_image_display(self):
        """更新图片显示"""
        if not self.current_image:
            return

        try:
            # 获取显示区域大小
            self.drop_area.update_idletasks()
            area_width = self.drop_area.winfo_width()
            area_height = self.drop_area.winfo_height()

            if area_width <= 10 or area_height <= 10:
                # 如果控件还没有正确初始化，延迟执行
                self.root.after(100, self.update_image_display)
                return

            # 计算图片的原始大小
            img_width, img_height = self.current_image.size

            # 计算适合显示区域的基础缩放比例（保持宽高比）
            max_width = area_width - 20  # 留出边距
            max_height = area_height - 20  # 留出边距

            base_scale = min(max_width / img_width, max_height / img_height)

            # 应用用户缩放因子
            final_scale = base_scale * self.zoom_factor

            # 计算最终显示尺寸
            display_width = int(img_width * final_scale)
            display_height = int(img_height * final_scale)

            # 确保图片不会超出控件边界
            if display_width > area_width - 10:
                scale_factor = (area_width - 10) / display_width
                display_width = int(display_width * scale_factor)
                display_height = int(display_height * scale_factor)

            if display_height > area_height - 10:
                scale_factor = (area_height - 10) / display_height
                display_width = int(display_width * scale_factor)
                display_height = int(display_height * scale_factor)

            # 缩放图片
            if display_width > 0 and display_height > 0:
                resized_image = self.current_image.resize((display_width, display_height), Image.Resampling.LANCZOS)

                # 转换为PhotoImage
                photo = ImageTk.PhotoImage(resized_image)

                # 更新显示（图片会自动在Label中居中）
                self.drop_area.configure(image=photo, text="", compound=tk.CENTER)
                self.drop_area.image = photo  # 保持引用

                # 更新缩放标签
                self.zoom_label.configure(text=f"{int(self.zoom_factor * 100)}%")

        except Exception as e:
            print(f"更新图片显示失败: {e}")

    def reinitialize_ocr(self):
        """重新初始化OCR引擎"""
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，无法重新初始化")
            return

        result = messagebox.askyesno(
            "重新初始化",
            "确定要重新初始化OCR引擎吗？\n这将重新加载模型文件。"
        )

        if result:
            self.ocr_processor = OCRProcessor()
            self.ocr_ready = False
            self.start_ocr_initialization()

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="选择图片 (Ctrl+O)", command=self.select_image)
        file_menu.add_command(label="粘贴图片 (Ctrl+V)", command=self.paste_image)
        file_menu.add_separator()
        file_menu.add_command(label="退出 (Ctrl+Q)", command=self.root.quit)

        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="复制文本 (Ctrl+C)", command=self.copy_text)
        edit_menu.add_command(label="清空 (Ctrl+R)", command=self.clear_results)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清理模型缓存", command=self.clear_model_cache_and_retry)
        tools_menu.add_separator()
        tools_menu.add_command(label="重新初始化OCR", command=self.reinitialize_ocr)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)

    def setup_keyboard_shortcuts(self):
        """设置键盘快捷键"""
        self.root.bind('<Control-v>', lambda _: self.paste_image())
        self.root.bind('<Control-V>', lambda _: self.paste_image())
        self.root.bind('<Control-o>', lambda _: self.select_image())
        self.root.bind('<Control-O>', lambda _: self.select_image())
        self.root.bind('<Control-c>', lambda _: self.copy_text())
        self.root.bind('<Control-C>', lambda _: self.copy_text())
        self.root.bind('<Control-r>', lambda _: self.clear_results())
        self.root.bind('<Control-R>', lambda _: self.clear_results())
        self.root.bind('<Control-q>', lambda _: self.root.quit())
        self.root.bind('<Control-Q>', lambda _: self.root.quit())

        # 让窗口可以获得焦点以接收键盘事件
        self.root.focus_set()

    def create_widgets(self):
        """创建UI组件"""
        # 创建菜单栏
        self.create_menu()

        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 左侧框架（图片区域）- 占更多空间
        left_frame = ttk.LabelFrame(main_frame, text="📷 图片区域 (支持拖拽、粘贴Ctrl+V)", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 图片控制面板
        image_control_frame = ttk.Frame(left_frame)
        image_control_frame.pack(fill=tk.X, pady=(0, 10))

        # GPU/CPU选择
        device_frame = ttk.LabelFrame(image_control_frame, text="🚀 处理模式", padding=5)
        device_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.device_var = tk.StringVar(value="auto")
        device_options = [
            ("🔍 自动检测", "auto"),
            ("🚀 GPU加速", "gpu"),
            ("💻 CPU模式", "cpu")
        ]

        for i, (text, value) in enumerate(device_options):
            rb = ttk.Radiobutton(
                device_frame,
                text=text,
                variable=self.device_var,
                value=value,
                command=self.on_device_change
            )
            rb.pack(side=tk.LEFT, padx=5)

        # 图片缩放控制
        zoom_frame = ttk.LabelFrame(image_control_frame, text="🔍 图片缩放", padding=5)
        zoom_frame.pack(side=tk.RIGHT, padx=(10, 0))

        self.zoom_label = ttk.Label(zoom_frame, text="100%")
        self.zoom_label.pack(side=tk.LEFT, padx=5)

        ttk.Button(zoom_frame, text="🔍+", command=self.zoom_in, width=4).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="🔍-", command=self.zoom_out, width=4).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="📐", command=self.fit_to_window, width=4).pack(side=tk.LEFT, padx=2)

        # 拖拽提示区域 - 改进设计
        self.drop_area = tk.Label(
            left_frame,
            text="🔄 正在初始化OCR引擎...\n请稍候",
            bg="#E3F2FD",
            fg="#1976D2",
            font=("Arial", 14, "bold"),
            relief=tk.GROOVE,
            bd=3
        )
        self.drop_area.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.drop_area.bind("<MouseWheel>", self.on_mouse_wheel)  # 鼠标滚轮缩放
        
        # 右侧框架（结果区域）- 调整宽度比例
        right_frame = ttk.LabelFrame(main_frame, text="📝 识别结果", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=(10, 0))
        right_frame.configure(width=400)  # 固定宽度

        # 文本显示区域 - 改进样式
        self.text_area = scrolledtext.ScrolledText(
            right_frame,
            wrap=tk.WORD,
            font=("Microsoft YaHei", 12),  # 更好的中文字体
            height=25,
            width=45,
            bg="#FAFAFA",
            fg="#333333",
            selectbackground="#2196F3",
            selectforeground="white"
        )
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 按钮框架 - 改进布局
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X)

        # 第一行按钮
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=(0, 8))

        # 复制按钮 - 更大更明显
        self.copy_button = ttk.Button(
            button_row1,
            text="📋 复制文本 (Ctrl+C)",
            command=self.copy_text,
            state=tk.DISABLED,
            width=20
        )
        self.copy_button.pack(side=tk.LEFT, padx=(0, 8))

        # 清空按钮
        self.clear_button = ttk.Button(
            button_row1,
            text="🗑️ 清空",
            command=self.clear_results,
            width=12
        )
        self.clear_button.pack(side=tk.LEFT)

        # 第二行按钮
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X)

        # 粘贴按钮
        self.paste_button = ttk.Button(
            button_row2,
            text="📋 粘贴图片 (Ctrl+V)",
            command=self.paste_image,
            width=20
        )
        self.paste_button.pack(side=tk.LEFT, padx=(0, 8))

        # 选择文件按钮
        self.select_button = ttk.Button(
            button_row2,
            text="📁 选择文件",
            command=self.select_image,
            width=12
        )
        self.select_button.pack(side=tk.LEFT)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.root,
            variable=self.progress_var,
            mode='indeterminate'
        )

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(
            self.root,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.drop_area.drop_target_register(DND_FILES)
        self.drop_area.dnd_bind('<<Drop>>', self.on_drop)
    
    def on_drop(self, event):
        """处理拖拽事件"""
        files = event.data.split()
        if files:
            file_path = files[0].strip('{}')  # 移除可能的大括号
            self.process_dropped_file(file_path)
    
    def select_image(self, event=None):
        """选择图片文件"""
        # 忽略event参数，仅用于绑定事件
        _ = event

        # 检查OCR是否已初始化
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        # 如果正在处理，不允许选择新图片
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        from tkinter import filedialog
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.process_dropped_file(file_path)
    
    def process_dropped_file(self, file_path):
        """处理选择或拖拽的文件"""
        # 检查OCR是否已初始化
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        # 如果正在处理，不允许处理新文件
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        # 检查文件类型
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext not in valid_extensions:
            messagebox.showerror("错误", "不支持的文件格式")
            return

        self.current_image_path = file_path
        self.display_image(file_path)
        self.start_ocr_processing(file_path)
    
    def display_image(self, image_path):
        """显示图片预览"""
        try:
            # 打开图片
            self.current_image = Image.open(image_path)

            # 重置缩放参数
            self.zoom_factor = 1.0
            self.image_offset_x = 0
            self.image_offset_y = 0

            # 更新显示
            self.update_image_display()

        except Exception as e:
            messagebox.showerror("错误", f"图片显示失败: {e}")
            self.current_image = None
    
    def start_ocr_processing(self, image_path):
        """开始OCR处理（异步）"""
        # 设置处理状态
        self.is_processing = True
        self.status_var.set("正在识别文字...")
        self.copy_button.configure(state=tk.DISABLED)
        self.clear_button.configure(state=tk.DISABLED)

        # 显示进度条
        self.progress_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.status_bar)
        self.progress_bar.start(10)  # 开始动画

        # 清空之前的结果
        self.text_area.delete(1.0, tk.END)
        self.text_area.insert(1.0, "正在识别中，请稍候...")

        # 在新线程中处理OCR
        thread = threading.Thread(
            target=self.ocr_worker,
            args=(image_path,),
            daemon=True
        )
        thread.start()
    
    def ocr_worker(self, image_path):
        """OCR工作线程"""
        try:
            result_text = self.ocr_processor.process_image(image_path)
            # 在主线程中更新UI
            self.root.after(0, self.update_results, result_text, None)
        except Exception as e:
            self.root.after(0, self.update_results, None, str(e))
    
    def update_results(self, text, error):
        """更新识别结果"""
        # 停止进度条并隐藏
        self.progress_bar.stop()
        self.progress_bar.pack_forget()

        # 重置处理状态
        self.is_processing = False
        self.clear_button.configure(state=tk.NORMAL)

        if error:
            self.status_var.set(f"识别失败: {error}")
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, f"识别失败: {error}")
            messagebox.showerror("错误", f"OCR处理失败: {error}")
        else:
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, text)
            self.copy_button.configure(state=tk.NORMAL)
            self.status_var.set("识别完成")
    
    def copy_text(self):
        """复制文本到剪贴板"""
        text = self.text_area.get(1.0, tk.END).strip()
        if text:
            pyperclip.copy(text)
            self.status_var.set("文本已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的文本")
    
    def clear_results(self):
        """清空结果"""
        # 如果正在处理，不允许清空
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，无法清空")
            return

        self.text_area.delete(1.0, tk.END)
        self.drop_area.configure(
            image="",
            text="📷 拖拽图片到此处\n\n📋 或按Ctrl+V粘贴"
        )
        self.drop_area.image = None
        self.copy_button.configure(state=tk.DISABLED)
        self.current_image_path = None
        self.status_var.set("就绪")

    def paste_image(self):
        """从剪贴板粘贴图片"""
        # 检查OCR是否已初始化
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        # 如果正在处理，不允许粘贴
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        try:
            # 从剪贴板获取数据
            clipboard_data = ImageGrab.grabclipboard()

            if clipboard_data is None:
                messagebox.showwarning("提示", "剪贴板中没有图片数据")
                return

            # 检查剪贴板数据类型
            if isinstance(clipboard_data, list):
                # 如果是文件路径列表，尝试处理第一个图片文件
                if clipboard_data and len(clipboard_data) > 0:
                    file_path = clipboard_data[0]
                    if os.path.exists(file_path):
                        # 检查是否为图片文件
                        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
                        file_ext = os.path.splitext(file_path)[1].lower()
                        if file_ext in valid_extensions:
                            self.process_dropped_file(file_path)
                            return
                        else:
                            messagebox.showwarning("提示", "剪贴板中的文件不是支持的图片格式")
                            return
                    else:
                        messagebox.showwarning("提示", "剪贴板中的文件路径无效")
                        return
                else:
                    messagebox.showwarning("提示", "剪贴板中没有有效的文件数据")
                    return

            # 检查是否为PIL图片对象
            if not hasattr(clipboard_data, 'save'):
                messagebox.showwarning("提示", "剪贴板中的数据不是有效的图片格式")
                return

            # 保存临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_path = temp_file.name
            temp_file.close()

            # 保存图片到临时文件
            clipboard_data.save(temp_path, 'PNG')

            # 处理图片
            self.current_image_path = temp_path
            self.display_image(temp_path)
            self.start_ocr_processing(temp_path)

            self.status_var.set("已粘贴图片，开始识别...")

        except Exception as e:
            messagebox.showerror("错误", f"粘贴图片失败: {e}")

    def show_help(self):
        """显示使用说明"""
        help_text = """
OCR文字识别工具 - 使用说明

📷 图片输入方式：
• 拖拽：直接将图片文件拖拽到左侧区域
• 粘贴：按Ctrl+V粘贴剪贴板中的图片
• 选择：点击左侧区域或按Ctrl+O选择文件

⌨️ 快捷键：
• Ctrl+V：粘贴图片
• Ctrl+O：选择图片文件
• Ctrl+C：复制识别结果
• Ctrl+R：清空结果
• Ctrl+Q：退出程序

📝 支持格式：
• 图片格式：JPG, PNG, BMP, GIF, TIFF
• 文字语言：中文、英文等多种语言

💡 使用技巧：
• 图片清晰度越高，识别效果越好
• 文字对比度高的图片识别更准确
• 支持截图工具直接粘贴
• 首次运行需要下载模型，请耐心等待
        """

        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("500x600")
        help_window.resizable(False, False)

        # 居中显示
        help_window.transient(self.root)
        help_window.grab_set()

        text_widget = scrolledtext.ScrolledText(
            help_window,
            wrap=tk.WORD,
            font=("Microsoft YaHei", 11),
            padx=20,
            pady=20
        )
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(1.0, help_text)
        text_widget.configure(state=tk.DISABLED)

        # 关闭按钮
        close_btn = ttk.Button(
            help_window,
            text="关闭",
            command=help_window.destroy
        )
        close_btn.pack(pady=10)

    def show_about(self):
        """显示关于对话框"""
        about_text = """
OCR文字识别工具 v1.0

基于PaddleOCR开发的图片文字识别工具

✨ 主要特性：
• 支持多种图片输入方式
• 高精度文字识别
• 简洁易用的界面
• 丰富的快捷键支持

🔧 技术栈：
• PaddleOCR - 文字识别引擎
• Tkinter - 用户界面
• PIL - 图片处理

📧 如有问题或建议，欢迎反馈！
        """

        messagebox.showinfo("关于", about_text)

    def run(self):
        """运行应用程序"""
        self.root.mainloop()


if __name__ == "__main__":
    try:
        app = OCRApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")
