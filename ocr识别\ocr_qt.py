import sys
import os
import time
import logging
import threading
from PySide6.QtCore import (Qt, QThread, Signal, Slot, QObject, QSize, QRectF, QEvent, QTimer, QPointF)
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QTextEdit, QLabel, QFileDialog, QSplitter, QGroupBox, QRadioButton,
    QScrollArea, QSizePolicy, QMenuBar, QMessageBox, QGridLayout, QButtonGroup,
    QDialog, QGraphicsDropShadowEffect
)
from PySide6.QtGui import (QPixmap, QImage, QPainter, QGuiApplication, QPen, QColor,
                         QAction, QTransform, QCursor, QKeySequence, QIcon, QConicalGradient)
from PIL import Image, ImageGrab
import pyperclip

# --- PaddleOCR setup ---
# It's better to handle this within the processor to keep UI code clean
# but for simplicity, we set it here.
os.environ['GLOG_minloglevel'] = '2'  # Suppress PaddlePaddle logging
try:
    from paddleocr import PaddleOCR
    import paddle
    PADDLE_AVAILABLE = True
except ImportError:
    PADDLE_AVAILABLE = False
    PaddleOCR = None
    paddle = None

# --- Loading Dialog ---
class QLoadingDialog(QDialog):
    """显示长时间操作进度的模态对话框。"""
    def __init__(self, parent=None, message="正在加载..."):
        super().__init__(parent)
        self.setWindowTitle("请稍候")
        self.setModal(True)
        # 移除标题栏和边框
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        
        # 创建半透明背景
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 创建主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 创建内容容器
        container = QWidget(self)
        container.setObjectName("container")
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(30, 30, 30, 30)
        container_layout.setSpacing(20)
        
        # 添加加载动画
        self.spinner = QLabel()
        self.spinner.setFixedSize(48, 48)
        self.spinner.setObjectName("spinner")
        container_layout.addWidget(self.spinner, 0, Qt.AlignCenter)
        
        # 添加加载提示文本
        self.label = QLabel(message)
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setObjectName("message")
        container_layout.addWidget(self.label)
        
        # 将容器添加到主布局
        layout.addWidget(container)
        
        # 设置固定大小
        self.setFixedSize(400, 200)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: rgba(0, 0, 0, 60);
            }
            QWidget#container {
                background-color: #ffffff;
                border-radius: 10px;
            }
            QLabel#message {
                font-family: "Microsoft YaHei";
                font-size: 14px;
                color: #333333;
                padding: 10px;
            }
            QLabel#spinner {
                min-width: 48px;
                min-height: 48px;
                background: transparent;
            }
        """)

        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 50))
        shadow.setOffset(0, 0)
        container.setGraphicsEffect(shadow)
        
        # 启动加载动画
        self.start_animation()

    def start_animation(self):
        """启动加载动画"""
        # 创建旋转动画
        self.rotation = 0
        self.animation_timer = QTimer(self)
        self.animation_timer.timeout.connect(self.update_rotation)
        self.animation_timer.start(30)  # 加快动画速度

    def update_rotation(self):
        """更新旋转角度"""
        self.rotation = (self.rotation + 8) % 360  # 减小每次旋转的角度
        
        # 创建渐变色圆环
        pixmap = QPixmap(48, 48)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 创建渐变
        center = QPointF(24, 24)
        gradient = QConicalGradient(center, -90)  # 从顶部开始
        gradient.setColorAt(0, QColor("#2196F3"))
        gradient.setColorAt(0.5, QColor("#4CAF50"))
        gradient.setColorAt(1, QColor("#2196F3"))
        
        # 绘制圆环
        pen = QPen()
        pen.setWidth(4)
        pen.setBrush(gradient)
        pen.setCapStyle(Qt.RoundCap)
        painter.setPen(pen)
        
        # 计算圆环的矩形区域
        rect = QRectF(4, 4, 40, 40)
        
        # 只旋转渐变，不移动圆环
        painter.translate(center)
        painter.rotate(self.rotation)
        painter.translate(-center)
        
        # 绘制圆环
        painter.drawArc(rect, 0, 360 * 16)  # 完整的圆
        painter.end()
        
        self.spinner.setPixmap(pixmap)

    def showEvent(self, event):
        """重写showEvent以确保对话框在显示时居中"""
        super().showEvent(event)
        # 等待主窗口完全显示
        if self.parent():
            self.parent().update()
            QApplication.processEvents()
            # 计算居中位置
            parent_rect = self.parent().geometry()
            x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - self.height()) // 2
            # 移动到居中位置
            self.move(x, y)

    def closeEvent(self, event):
        """重写closeEvent以停止动画"""
        if hasattr(self, 'animation_timer'):
            self.animation_timer.stop()
        super().closeEvent(event)

# --- Custom Zoomable Image Label ---
class ZoomableImageLabel(QLabel):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setScaledContents(False)
        self.setAlignment(Qt.AlignCenter)
        self.setCursor(Qt.OpenHandCursor)
        self._pixmap = QPixmap()
        self.scale_factor = 1.0
        self.scroll_area = None
        self.is_dragging = False
        self.last_mouse_pos = None
        
        # 设置默认提示文本的样式
        self.setStyleSheet("""
            QLabel {
                color: #757575;
                background-color: white;
                border-radius: 8px;
            }
        """)
        
        # 创建默认提示文本
        self.default_text = ("请拖拽、粘贴或选择一张图片\n\n"
                             "支持以下操作：\n"
                             "• 拖放图片到此处\n"
                             "• 复制图片后按 Ctrl+V 粘贴\n"
                             "• 点击\"选择图片\"按钮\n\n"
                             "图片载入后：\n"
                             "• 使用鼠标滚轮缩放\n"
                             "• 按住左键拖动平移")
        self.setText(self.default_text)

    def set_scroll_area(self, scroll_area):
        self.scroll_area = scroll_area

    def setPixmap(self, pixmap):
        """Sets a new pixmap and clears the default text."""
        self._pixmap = pixmap
        self.setText("")  # Clear the text when showing an image
        self.setStyleSheet("")  # Reset the style
        self.update_scaled_pixmap()

    def clearPixmap(self):
        """Clears the pixmap and restores the default text."""
        self._pixmap = QPixmap()
        self.setText(self.default_text)
        self.setStyleSheet("""
            QLabel {
                color: #757575;
                background-color: white;
                border-radius: 8px;
            }
        """)

    def update_scaled_pixmap(self):
        if not self._pixmap.isNull():
            size = self._pixmap.size() * self.scale_factor
            scaled_pixmap = self._pixmap.scaled(size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            super().setPixmap(scaled_pixmap)
            self.resize(scaled_pixmap.size()) # Resize label to fit pixmap

    def wheelEvent(self, event):
        if not self._pixmap.isNull():
            # Zoom In
            if event.angleDelta().y() > 0:
                self.scale_factor *= 1.25
            # Zoom Out
            else:
                self.scale_factor /= 1.25
            self.update_scaled_pixmap()

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton and not self._pixmap.isNull():
            self.is_dragging = True
            self.last_mouse_pos = event.pos()
            self.setCursor(Qt.ClosedHandCursor)

    def mouseMoveEvent(self, event):
        if self.is_dragging and self.scroll_area:
            delta = event.pos() - self.last_mouse_pos
            
            h_bar = self.scroll_area.horizontalScrollBar()
            v_bar = self.scroll_area.verticalScrollBar()

            h_bar.setValue(h_bar.value() - delta.x())
            v_bar.setValue(v_bar.value() - delta.y())
            
            self.last_mouse_pos = event.pos()

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            self.setCursor(Qt.OpenHandCursor)

            
# --- OCR Processor (Adapted for Qt) ---

class OcrWorker(QObject):
    """Worker for running OCR initialization or processing in a thread."""
    finished = Signal(bool, str)  # Success/Fail, Message/Result

    def __init__(self, processor, image_path=None, image_data=None, device_type='auto'):
        super().__init__()
        self.processor = processor
        self.image_path = image_path
        self.image_data = image_data
        self.device_type = device_type

    @Slot()
    def run(self):
        """Run initialization or processing."""
        try:
            if self.image_path:
                result_text = self.processor.process_image(self.image_path)
                self.finished.emit(True, result_text)
            elif self.image_data:
                result_text = self.processor.process_image_data(self.image_data)
                self.finished.emit(True, result_text)
            else:
                message = self.processor.initialize_ocr(self.device_type)
                self.finished.emit(True, message)
        except Exception as e:
            self.finished.emit(False, str(e))


class OCRProcessor:
    """Handles the OCR engine and processing."""
    def __init__(self):
        self.ocr = None
        self.is_initialized = False
        self.device_type = 'auto'
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    def check_gpu_support(self):
        """Check if a CUDA-enabled GPU is available."""
        if not PADDLE_AVAILABLE:
            return False
        try:
            return paddle.device.is_compiled_with_cuda() and paddle.device.cuda.device_count() > 0
        except Exception as e:
            self.logger.warning(f"Error checking GPU support: {e}")
            return False

    def initialize_ocr(self, device_type='auto'):
        """
        Initializes the OCR engine.
        Returns a status message.
        """
        if not PADDLE_AVAILABLE:
            raise ImportError("PaddleOCR or PaddlePaddle is not installed.")

        init_start_time = time.time()
        self.device_type = device_type
        use_gpu = False

        if device_type == 'auto':
            if self.check_gpu_support():
                use_gpu = True
                mode_name = "GPU (Auto-detected)"
            else:
                use_gpu = False
                mode_name = "CPU (Auto-detected)"
        elif device_type == 'gpu':
            if not self.check_gpu_support():
                raise RuntimeError("GPU support requested, but not available or drivers are incorrect.")
            use_gpu = True
            mode_name = "GPU (User-selected)"
        else: # cpu
            use_gpu = False
            mode_name = "CPU (User-selected)"

        self.logger.info(f"Initializing OCR with {mode_name}...")
        try:
            # Re-initialize to apply new settings
            self.ocr = PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=use_gpu, show_log=False)
            self.is_initialized = True
            elapsed_time = time.time() - init_start_time
            message = f"OCR 引擎成功初始化 {mode_name}. 时间: {elapsed_time:.2f}s"
            self.logger.info(message)
            return message
        except Exception as e:
            self.is_initialized = False
            self.ocr = None
            error_message = f"初始化 OCR 失败 {mode_name}: {e}"
            self.logger.error(error_message)
            # Try CPU as a fallback if auto/gpu failed
            if use_gpu and device_type != 'cpu':
                self.logger.info("回滚到 CPU 初始化...")
                return self.initialize_ocr(device_type='cpu')
            raise RuntimeError(error_message)

    def process_image(self, image_path):
        """
        Processes a single image file and returns the OCR text.
        """
        if not self.is_initialized or not self.ocr:
            raise RuntimeError("OCR engine is not initialized.")
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")

        self.logger.info(f"Processing image: {image_path}")
        start_time = time.time()

        # The result from PaddleOCR is a list of lists.
        result = self.ocr.ocr(image_path, cls=True)

        processing_time = time.time() - start_time
        self.logger.info(f"Image processing finished in {processing_time:.2f}s")

        if not result or not result[0]:
            return "No text detected."

        # Extract text from the result structure
        lines = [line[1][0] for line in result[0]]
        return "\n".join(lines)

    def process_image_data(self, image_data):
        """Processes image data from memory."""
        if not self.is_initialized or not self.ocr:
            raise RuntimeError("OCR 引擎未初始化。")

        self.logger.info("根据内存数据处理图像")
        start_time = time.time()
        
        # Use a temporary file to handle in-memory data, as it's the most
        # compatible way to pass data to the PaddleOCR engine.
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
            temp_file.write(image_data)
            temp_file_path = temp_file.name
        
        try:
            result = self.ocr.ocr(temp_file_path, cls=True)
        finally:
            os.unlink(temp_file_path) # Ensure temp file is deleted

        processing_time = time.time() - start_time
        self.logger.info(f"图像处理完成 {processing_time:.2f}s")

        if not result or not result[0]:
            return "未检测到文本。"

        # Extract text from the result structure
        lines = [line[1][0] for line in result[0]]
        return "\n".join(lines)

# --- Main Application Window ---
STYLE_SHEET = """
QWidget {
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
    color: #2c3e50;
}

QMainWindow {
    background-color: #f8f9fa;
}

QGroupBox {
    background-color: #ffffff;
    border: none;
    border-radius: 8px;
    padding: 12px;
    margin: 0px;
}

QRadioButton {
    background-color: transparent;
    padding: 8px 12px;
    border-radius: 6px;
    spacing: 4px;
    min-width: 90px;
}

QRadioButton:hover {
    background-color: #f8f9fa;
}

QRadioButton:checked {
    background-color: #e8f0fe;
    color: #1a73e8;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border-radius: 9px;
    border: 2px solid #bdc3c7;
    margin-right: 6px;
}

QRadioButton::indicator:checked {
    background-color: #1a73e8;
    border: 2px solid #1a73e8;
}

QRadioButton::indicator:unchecked {
    background-color: transparent;
}

QRadioButton:disabled {
    color: #bdc3c7;
}

QPushButton {
    background-color: #1a73e8;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 6px;
    font-weight: bold;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #1557b0;
}

QPushButton:pressed {
    background-color: #0d47a1;
}

QPushButton:disabled {
    background-color: #e0e0e0;
    color: #9e9e9e;
}

QTextEdit {
    background-color: white;
    border: none;
    border-radius: 8px;
    padding: 12px;
    selection-background-color: #e3f2fd;
    selection-color: #1a73e8;
}

QScrollArea {
    border: none;
    border-radius: 8px;
    background-color: white;
}

QScrollBar:vertical {
    border: none;
    background: #f8f9fa;
    width: 8px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background: #bdc3c7;
    min-height: 20px;
    border-radius: 4px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background: #f8f9fa;
    height: 8px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background: #bdc3c7;
    min-width: 20px;
    border-radius: 4px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QSplitter::handle {
    background: #e0e0e0;
}

QSplitter::handle:horizontal {
    width: 1px;
}

QSplitter::handle:vertical {
    height: 1px;
}

QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
}

QMenuBar::item {
    padding: 6px 12px;
    color: #2c3e50;
}

QMenuBar::item:selected {
    background-color: #e8f0fe;
    color: #1a73e8;
}

QMenu {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 4px;
}

QMenu::item {
    padding: 6px 28px 6px 12px;
    border-radius: 4px;
    margin: 2px;
}

QMenu::item:selected {
    background-color: #e8f0fe;
    color: #1a73e8;
}

QLabel {
    color: #2c3e50;
}
"""

class OCRApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI大模型OCR识别工具")
        self.setGeometry(100, 100, 1200, 800)

        self.ocr_processor = OCRProcessor()
        self.current_image_path = None
        self.current_image_data = None # For clipboard images
        self.loading_dialog = None
        self.thread = None
        self.worker = None

        self._create_widgets()
        self._create_layouts()
        self._create_connections()
        self._create_menu()
        self.setAcceptDrops(True)
        self.setStyleSheet(STYLE_SHEET)

        self.reinitialize_ocr()
        
        self.center_window()

    def center_window(self):
        """Centers the main window on the screen."""
        screen = QGuiApplication.primaryScreen().geometry()
        size = self.geometry()
        self.move((screen.width() - size.width()) / 2,
                  (screen.height() - size.height()) / 2)

    def _create_widgets(self):
        """Create all the UI widgets."""
        # Central Widget
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # --- Left Panel (Image Display) ---
        self.image_scroll_area = QScrollArea()
        self.image_label = ZoomableImageLabel()
        self.image_label.set_scroll_area(self.image_scroll_area)
        
        # Use a simple widget container for the scroll area
        self.image_scroll_area.setWidget(self.image_label)
        self.image_scroll_area.setWidgetResizable(True)

        # --- Right Panel (Controls & Results) ---
        # Device Selection
        self.device_groupbox = QGroupBox()
        
        # Create radio buttons with icons
        self.radio_auto = QRadioButton(" 自动检测")
        self.radio_gpu = QRadioButton(" GPU加速")
        self.radio_cpu = QRadioButton(" CPU模式")
        
        # Set icons for radio buttons
        self.radio_auto.setIcon(QIcon.fromTheme("system-search"))
        self.radio_gpu.setIcon(QIcon.fromTheme("computer"))
        self.radio_cpu.setIcon(QIcon.fromTheme("cpu"))
        
        self.radio_auto.setChecked(True)

        self.device_button_group = QButtonGroup(self)
        self.device_button_group.addButton(self.radio_auto)
        self.device_button_group.addButton(self.radio_gpu)
        self.device_button_group.addButton(self.radio_cpu)

        if not self.ocr_processor.check_gpu_support():
            self.radio_gpu.setEnabled(False)
            self.radio_gpu.setToolTip("未检测到 GPU 或 CUDA 未正确安装")

        # Results Text Area
        self.results_text = QTextEdit()
        self.results_text.setPlaceholderText("OCR 识别结果将显示在此处。")
        self.results_text.setReadOnly(True)

        # Action Buttons with icons
        self.select_button = QPushButton(" 选择图片")
        self.select_button.setIcon(QIcon("icons/select_icon.svg"))
        self.paste_button = QPushButton(" 粘贴图片")
        self.paste_button.setIcon(QIcon("icons/paste_icon.svg"))
        self.fit_button = QPushButton(" 适应窗口")
        self.fit_button.setIcon(QIcon("icons/fit_icon.svg"))
        self.copy_button = QPushButton(" 复制结果")
        self.copy_button.setIcon(QIcon("icons/copy_icon.svg"))
        self.clear_button = QPushButton(" 清空所有")
        self.clear_button.setIcon(QIcon("icons/clear_icon.svg"))

    def _create_layouts(self):
        """Organize widgets into layouts."""
        # Device selection layout
        device_layout = QHBoxLayout(self.device_groupbox)
        device_layout.setContentsMargins(0, 0, 0, 0)
        device_layout.addWidget(self.radio_auto)
        device_layout.addWidget(self.radio_gpu)
        device_layout.addWidget(self.radio_cpu)
        device_layout.addStretch()

        # Button layout
        button_layout = QGridLayout()
        button_layout.setSpacing(10)
        button_layout.addWidget(self.select_button, 0, 0)
        button_layout.addWidget(self.paste_button, 0, 1)
        button_layout.addWidget(self.fit_button, 0, 2)
        button_layout.addWidget(self.copy_button, 1, 0, 1, 2)
        button_layout.addWidget(self.clear_button, 1, 2)
        

        # Right panel layout
        right_layout = QVBoxLayout()
        right_layout.setSpacing(15)
        right_layout.addWidget(self.device_groupbox)
        right_layout.addWidget(self.results_text, 1)
        right_layout.addLayout(button_layout)
        right_widget = QWidget()
        right_widget.setLayout(right_layout)

        # Main splitter layout
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.addWidget(self.image_scroll_area)
        self.splitter.addWidget(right_widget)
        self.splitter.setSizes([700, 500])
        self.splitter.setHandleWidth(1)

        # Set the main layout for the central widget
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.addWidget(self.splitter)

    def _create_connections(self):
        """Connect signals and slots."""
        # Buttons
        self.select_button.clicked.connect(self.select_image)
        self.paste_button.clicked.connect(self.paste_image)
        self.fit_button.clicked.connect(self.fit_image_to_window)
        self.copy_button.clicked.connect(self.copy_results)
        self.clear_button.clicked.connect(self.clear_all)

        # OCR Engine Radio Buttons
        self.radio_auto.toggled.connect(self.reinitialize_ocr)
        self.radio_cpu.toggled.connect(self.reinitialize_ocr)
        self.radio_gpu.toggled.connect(self.reinitialize_ocr)
        
        # Install event filter for direct pasting on the image area
        self.image_scroll_area.installEventFilter(self)

    def _create_menu(self):
        """Create the main menu bar."""
        self.menu_bar = self.menuBar()
        # File Menu
        file_menu = self.menu_bar.addMenu("文件(&F)")
        select_action = QAction("选择图片(&S)...", self)
        select_action.triggered.connect(self.select_image)
        file_menu.addAction(select_action)

        paste_action = QAction("粘贴图片(&P)", self)
        paste_action.triggered.connect(self.paste_image)
        file_menu.addAction(paste_action)

        file_menu.addSeparator()
        exit_action = QAction("退出(&X)", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help Menu
        help_menu = self.menu_bar.addMenu("帮助(&H)")
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)


    # --- Slots and Event Handlers ---

    def eventFilter(self, watched, event):
        """Filter events for specific widgets, like handling paste shortcut."""
        if watched == self.image_scroll_area and event.type() == QEvent.KeyPress:
            if event.matches(QKeySequence.Paste):
                self.paste_image()
                return True  # Event was handled
        
        return super().eventFilter(watched, event)

    @Slot()
    def reinitialize_ocr(self, checked=True):
        """Starts the OCR initialization in a background thread."""
        if not isinstance(checked, bool) or not checked:
            return
            
        if not PADDLE_AVAILABLE:
            self.statusBar().showMessage("错误: 未找到 PaddleOCR 库，请安装。")
            QMessageBox.critical(self, "缺少库", "未安装 PaddleOCR。此应用需要该库才能运行。\n\n请运行: pip install paddlepaddle paddleocr")
            return

        device_type = 'auto'
        if self.radio_cpu.isChecked():
            device_type = 'cpu'
        elif self.radio_gpu.isChecked():
            device_type = 'gpu'

        self.statusBar().showMessage(f"正在初始化 OCR 引擎 ({device_type})... 请稍候。")
        self.select_button.setEnabled(False)
        self.paste_button.setEnabled(False)

        # 创建加载对话框
        self.loading_dialog = QLoadingDialog(
            self, message=f"正在初始化 {device_type.upper()} 引擎..."
        )

        # 使用QTimer延迟显示对话框，确保主窗口完全显示和居中
        QTimer.singleShot(10, self.loading_dialog.show)

        self.thread = QThread()
        self.worker = OcrWorker(self.ocr_processor, device_type=device_type)
        self.worker.moveToThread(self.thread)

        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_ocr_initialization_finished)
        self.thread.start()

    @Slot(bool, str)
    def on_ocr_initialization_finished(self, success, message):
        """Handles completion of the OCR initialization worker."""
        if self.loading_dialog:
            self.loading_dialog.close()
            self.loading_dialog = None

        self.statusBar().showMessage(message, 10000)
        self.select_button.setEnabled(True)
        self.paste_button.setEnabled(True)
        
        if not success:
            QMessageBox.critical(self, "OCR 错误", f"初始化 OCR 引擎失败:\n{message}")

        self.thread.quit()
        self.thread.wait()

    @Slot()
    def select_image(self):
        """Opens a file dialog to select an image."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图片", "", "图片文件 (*.png *.jpg *.jpeg *.bmp *.webp)"
        )
        if file_path:
            self.load_and_process_image(file_path=file_path)

    @Slot()
    def paste_image(self):
        """Handles pasting an image from the clipboard."""
        try:
            image = ImageGrab.grabclipboard()
            if isinstance(image, Image.Image):
                import io
                img_byte_arr = io.BytesIO()
                # Ensure image has an alpha channel for consistency if needed, but PNG handles it well.
                image.save(img_byte_arr, format='PNG')
                image_data = img_byte_arr.getvalue()
                self.load_and_process_image(image_data=image_data)
            else:
                self.statusBar().showMessage("剪贴板中没有找到图片。", 5000)
        except Exception as e:
            self.statusBar().showMessage(f"粘贴图片时出错: {e}", 5000)

    def load_and_process_image(self, image_path=None, image_data=None):
        """Loads, displays, and starts OCR processing for an image."""
        self.results_text.clear()
        pixmap = QPixmap()
        
        if image_path:
            self.current_image_path = image_path
            self.current_image_data = None
            pixmap.load(image_path)
            self.statusBar().showMessage(f"已加载图片: {os.path.basename(image_path)}", 5000)
        elif image_data:
            self.current_image_path = None
            self.current_image_data = image_data
            pixmap.loadFromData(image_data)
            self.statusBar().showMessage("已从剪贴板加载图片", 5000)
        else:
            return

        if pixmap.isNull():
            self.statusBar().showMessage("错误：无效的图片文件。", 5000)
            return

        self.image_label.setPixmap(pixmap)
        self.fit_image_to_window()
        self.results_text.setPlainText("正在识别中...")
        self.start_ocr_processing()

    def start_ocr_processing(self):
        """Starts the OCR processing in a background thread."""
        if not (self.current_image_path or self.current_image_data) or not self.ocr_processor.is_initialized:
            self.statusBar().showMessage("OCR 引擎未就绪或没有图片。", 5000)
            return
        
        self.statusBar().showMessage(f"正在处理图片...")
        self.loading_dialog = QLoadingDialog(self, message="正在识别文字...")
        self.loading_dialog.show()
        
        self.thread = QThread()
        self.worker = OcrWorker(self.ocr_processor, 
                               image_path=self.current_image_path,
                               image_data=self.current_image_data)
        self.worker.moveToThread(self.thread)

        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_ocr_processing_finished)
        self.thread.start()

    @Slot(bool, str)
    def on_ocr_processing_finished(self, success, result):
        """Handles the result of the OCR processing."""
        if self.loading_dialog:
            self.loading_dialog.close()
            self.loading_dialog = None
            
        self.results_text.setPlainText(result)
        self.statusBar().showMessage("处理完成。", 5000)
        if not success:
            self.statusBar().showMessage(f"处理过程中发生错误: {result}", 5000)

        self.thread.quit()
        self.thread.wait()

    @Slot()
    def copy_results(self):
        """Copies the result text to the clipboard."""
        pyperclip.copy(self.results_text.toPlainText())
        self.statusBar().showMessage("结果已复制到剪贴板。", 3000)

    @Slot()
    def clear_all(self):
        """Clears image, results, and status."""
        self.image_label.clearPixmap()
        self.results_text.clear()
        self.current_image_path = None
        self.current_image_data = None
        self.statusBar().showMessage("已清空。", 3000)

    @Slot()
    def fit_image_to_window(self):
        """Fits the image to the available window size."""
        if self.image_label._pixmap.isNull():
            return
        
        viewport_size = self.image_scroll_area.viewport().size()
        pixmap_size = self.image_label._pixmap.size()
        
        if pixmap_size.isEmpty():
            return
            
        viewport_ratio = viewport_size.width() / viewport_size.height()
        pixmap_ratio = pixmap_size.width() / pixmap_size.height()

        if viewport_ratio > pixmap_ratio:
            new_scale = viewport_size.height() / pixmap_size.height()
        else:
            new_scale = viewport_size.width() / pixmap_size.width()
        
        self.image_label.scale_factor = new_scale * 0.98 # Add a small margin
        self.image_label.update_scaled_pixmap()

    def show_about(self):
        """Displays the about dialog."""
        QMessageBox.about(self, "关于",
            "<h2>高级 OCR 识别工具</h2>"
            "<p>一个使用 PySide6 和 PaddleOCR 构建的桌面应用。</p>"
            "<p>允许用户选择、粘贴、拖放图片文件，并使用CPU或GPU进行文字识别。</p>"
            "<p>支持图片缩放和拖拽。</p>"
        )


    # --- Event Handlers for Drag-n-Drop ---
    def dragEnterEvent(self, event):
        mime_data = event.mimeData()
        if mime_data.hasUrls() and len(mime_data.urls()) == 1:
            url = mime_data.urls()[0]
            # Check for image file extensions
            if url.isLocalFile() and url.toLocalFile().lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
                event.acceptProposedAction()

    def dropEvent(self, event):
        url = event.mimeData().urls()[0]
        file_path = url.toLocalFile()
        self.load_and_process_image(image_path=file_path)

    def resizeEvent(self, event):
        """Handle window resize events, e.g., to refit image."""
        super().resizeEvent(event)
        
# --- Main execution ---
def main():
    """Main function to run the application."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Global exception hook
    def excepthook(exc_type, exc_value, exc_tb):
        import traceback
        tb_text = "".join(traceback.format_exception(exc_type, exc_value, exc_tb))
        logging.error(f"Unhandled exception:\n{tb_text}")
        QMessageBox.critical(None, "致命错误", f"发生了一个未处理的严重错误:\n{exc_value}\n\n详情请查看日志。")

    sys.excepthook = excepthook

    app = QApplication(sys.argv)
    window = OCRApp()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
